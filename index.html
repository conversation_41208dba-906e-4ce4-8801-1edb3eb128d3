<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forge - A Developer's Journal</title>
    <meta name="description" content="A personal exploration into technology, writing, and marketing. A digital garden.">
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400..700;1,400..700&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest" defer></script>

    <style>
        :root {
            --bg-parchment: #fbfaf8;
            --text-charcoal: #2d2d2d;
            --text-muted: #6b6b6b;
            --border-soft: #e5e3e0;
            --accent-ember: #d9480f; /* A fiery, burnt orange */
            --highlight-bg: #ffe8cc;
            --highlight-text: #663300;
            --surface: #f5f3f1;
        }

        [data-theme="dark"] {
            --bg-parchment: #111111;
            --surface: #1e1e1e;
            --text-charcoal: #d1d1d1;
            --text-muted: #999999;
            --border-soft: #333333;
            --accent-ember: #ff6b35;
            --highlight-bg: #2c3a4a;
            --highlight-text: #ffffff;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-parchment);
            color: var(--text-charcoal);
            /* --- The Analog Touch: Subtle Texture --- */
            background-image: url('data:image/svg+xml,%3Csvg width="6" height="6" viewBox="0 0 6 6" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%239C92AC" fill-opacity="0.04" fill-rule="evenodd"%3E%3Cpath d="M5 0h1L0 6V5zM6 5v1H5z"/%3E%3C/g%3E%3C/svg%3E');
            transition: background-color 0.3s ease, color 0.3s ease;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* --- Smooth Animations --- */
        * {
            transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease;
        }

        /* --- Screen Reader Only --- */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
        
        #progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-ember), #ff6b35);
            z-index: 100;
            transition: width 0.1s linear;
            box-shadow: 0 0 10px rgba(217, 72, 15, 0.3);
        }

        /* --- Typography with Personality --- */
        .prose {
            font-family: 'Lora', serif; /* A more bookish, classic serif */
            font-size: 1rem;
            line-height: 1.75;
            max-width: 45em;
            width: 100%;
        }
        .prose h1, .prose h2, .prose h3 {
            font-family: 'Inter', sans-serif; font-weight: 600;
            letter-spacing: -0.01em; scroll-margin-top: 5rem;
            position: relative; color: var(--text-charcoal);
        }
        .prose h1 {
            font-size: 2rem;
            letter-spacing: -0.02em;
            line-height: 1.2;
        }
        .prose h2 {
            margin-top: 2.5em;
            padding-bottom: 0.3em;
            border-bottom: 1px solid var(--border-soft);
            font-size: 1.5rem;
        }
        .prose h3 {
            font-size: 1.25rem;
            margin-top: 2em;
        }
        .prose h2 .heading-link, .prose h3 .heading-link {
            position: absolute;
            left: -1.25em;
            top: 0.25em;
            opacity: 0;
            transition: opacity 0.2s;
            color: var(--accent-ember);
            text-decoration: none;
        }
        .prose h2:hover .heading-link, .prose h3:hover .heading-link {
            opacity: 1;
        }

        /* --- Responsive Typography --- */
        @media (min-width: 640px) {
            .prose {
                font-size: 1.125rem;
                line-height: 1.85;
            }
            .prose h1 {
                font-size: 2.5rem;
            }
            .prose h2 {
                font-size: 1.75rem;
            }
            .prose h3 {
                font-size: 1.375rem;
            }
        }

        /* --- Human-touch Links & Highlights --- */
        .prose a {
            color: var(--accent-ember);
            text-decoration: none;
            box-shadow: 0 1px 0 0 var(--accent-ember); /* A more subtle underline */
            transition: all 0.2s ease-in-out;
        }
        .prose a:hover {
            background-color: var(--highlight-bg);
            box-shadow: 0 1px 0 0 var(--accent-ember);
        }
        .highlight {
            background: linear-gradient(180deg, transparent 65%, var(--highlight-bg) 65%);
            color: inherit;
        }
        
        .prose blockquote {
            color: var(--text-muted);
            border-left: 4px solid var(--accent-ember);
            font-style: italic;
            padding: 1rem 1.5rem;
            margin: 2rem 0;
            background: linear-gradient(135deg, var(--surface), var(--bg-parchment));
            border-radius: 0 8px 8px 0;
            position: relative;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .prose blockquote::before {
            content: '"';
            font-size: 3rem;
            color: var(--accent-ember);
            position: absolute;
            top: -0.5rem;
            left: 1rem;
            opacity: 0.3;
            font-family: Georgia, serif;
        }

        /* --- The Note Status System --- */
        .note-status {
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 1.25rem;
            margin-bottom: 2rem;
            border: 2px dashed var(--border-soft);
            border-radius: 12px;
            background-color: var(--surface);
            font-family: 'Inter', sans-serif;
            font-size: 0.9rem;
            color: var(--text-muted);
            position: relative;
            overflow: hidden;
        }
        .note-status::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-ember), #ff6b35);
        }
        .note-status strong {
            color: var(--text-charcoal);
        }
        .note-status--seedling {
            background: linear-gradient(135deg, #f1f8e9, #e8f5e8);
            border-color: #81c784;
            border-style: dotted;
        }
        .note-status--seedling::before {
            background: linear-gradient(90deg, #4caf50, #81c784);
        }
        .note-status--evergreen {
            background: linear-gradient(135deg, #e3f2fd, #e1f5fe);
            border-color: #64b5f6;
            border-style: solid;
        }
        .note-status--evergreen::before {
            background: linear-gradient(90deg, #2196f3, #64b5f6);
        }
        

        /* --- Desktop Sidebar --- */
        .desktop-sidebar-container { display: none; }
        @media (min-width: 1100px) {
            .desktop-sidebar-container {
                display: block; position: absolute; top: 0; left: 100%;
                width: 250px; padding-left: 3.5rem;
            }
            .prose {
                width: calc(100% - 320px); /* Make room for the sidebar */
                margin-left: 0;
            }
        }
        .sidebar-content {
            font-family: 'Inter', sans-serif; font-size: 0.8rem; line-height: 1.5;
            color: var(--text-muted); position: sticky; top: 4rem;
            max-height: calc(100vh - 8rem); overflow-y: auto;
        }

        /* Shared Sidebar Section Styles */
        .sidebar-section-header {
            display: flex; justify-content: space-between; align-items: center;
            font-weight: 600; color: var(--text-charcoal); border-bottom: 1px solid var(--border-soft);
            padding-bottom: 0.5rem; margin-bottom: 0.75rem; user-select: none;
        }
        .sidebar-section-header h3 { font-size: 0.8rem; }
        .sidebar-section.collapsible .sidebar-section-header { cursor: pointer; }
        .sidebar-section-header .chevron { transition: transform 0.2s ease-in-out; }
        .sidebar-section.collapsed .chevron { transform: rotate(-90deg); }
        .toc-link {
            display: block; padding: 0.3rem 0.5rem; border-left: 2px solid transparent;
            transition: all 0.15s ease-in-out;
        }
        .toc-link.h3 { margin-left: 1rem; }
        .toc-link:hover { background-color: var(--surface); }
        .toc-active {
            color: var(--text-charcoal); font-weight: 600;
            border-left-color: var(--accent-ember) !important;
            background-color: var(--highlight-bg);
        }

        .backlink-context {
            font-size: 0.75rem;
            color: var(--text-muted);
            border-left: 2px solid var(--border-soft);
            padding-left: 0.75rem;
            margin-top: 0.25rem;
            margin-left: 0.25rem;
        }

        /* --- Tablet Layout --- */
        @media (min-width: 768px) and (max-width: 1199px) {
            .prose {
                max-width: 42rem;
                margin: 0 auto;
            }
        }

        /* --- Mobile Layout --- */
        @media (max-width: 767px) {
            .prose {
                max-width: 100%;
                margin: 0 auto;
                padding: 0 1rem;
            }
            .prose h1 {
                font-size: 1.75rem;
            }
            .prose h2 {
                font-size: 1.375rem;
                margin-top: 2em;
            }
            .note-status {
                margin: 1rem 0;
                padding: 1rem;
            }
        }



        /* --- Scroll to Top Button --- */
        .scroll-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            background: var(--accent-ember);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(217, 72, 15, 0.3);
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .scroll-to-top:hover {
            background: var(--text-charcoal);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(217, 72, 15, 0.4);
        }
        .scroll-to-top:focus {
            outline: 2px solid var(--accent-ember);
            outline-offset: 2px;
        }

        /* --- Mobile Bottom Sheet Navigation --- */
        #mobile-nav-overlay {
            position: fixed; inset: 0; z-index: 45;
            background-color: rgba(0,0,0,0.2);
            backdrop-filter: blur(3px);
            opacity: 0; visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        #mobile-nav-sheet {
            position: fixed; bottom: 0; left: 0; right: 0; z-index: 50;
            background-color: var(--bg-parchment);
            border-top: 1px solid var(--border-soft);
            border-radius: 16px 16px 0 0;
            padding: 1rem 1rem 1.5rem;
            max-height: 70vh;
            overflow-y: auto;
            transform: translateY(100%);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        #mobile-nav-sheet.open { transform: translateY(0); }
        #mobile-nav-overlay.open { opacity: 1; visibility: visible; }
        #mobile-nav-handle {
            width: 40px; height: 4px;
            background-color: var(--border-soft);
            border-radius: 2px;
            margin: 0 auto 1rem;
        }
    </style>
</head>
<body class="py-6 px-4 sm:px-6 lg:px-8">
    <div id="progress-bar"></div>
    
    <header class="max-w-7xl mx-auto w-full flex justify-between items-center mb-12" role="banner">
        <a href="#" class="font-bold text-lg flex items-center gap-2" aria-label="Forge - A Developer's Journal Homepage">
            <i data-lucide="flame" class="w-5 h-5 text-orange-600" aria-hidden="true"></i>
            <span>Forge</span>
        </a>
        <div class="flex items-center gap-2">
            <button id="theme-toggle" class="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800" aria-label="Toggle dark mode">
                <i data-lucide="sun" class="w-5 h-5 block dark:hidden"></i>
                <i data-lucide="moon" class="w-5 h-5 hidden dark:block"></i>
            </button>
            <button id="mobile-menu-button" class="lg:hidden p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800" aria-label="Open menu" aria-controls="mobile-nav-sheet" aria-expanded="false">
                <i data-lucide="menu" class="w-6 h-6"></i>
            </button>
            <nav class="hidden lg:flex items-center gap-4 text-sm font-medium text-gray-600 ml-4" role="navigation" aria-label="Main navigation">
                <a href="#" class="hover:text-black transition-colors duration-200" aria-current="page">Home</a>
                <a href="#" class="hover:text-black transition-colors duration-200">Garden</a>
                <a href="#" class="hover:text-black transition-colors duration-200">About</a>
            </nav>
        </div>
    </header>

    <div class="relative w-full max-w-5xl mx-auto">
        <main class="prose mx-auto" role="main">
            <header class="article-header">
                <h1 id="main-title">What's Up With Web3?</h1>
                <div class="text-sm text-gray-500 mt-4 space-x-2" role="contentinfo">
                    <span>Published: <time datetime="2025-06-15">15 Jun 2025</time></span>
                    <span class="text-gray-300" aria-hidden="true">&middot;</span>
                    <span>Updated: <time datetime="2025-06-28">28 Jun 2025</time></span>
                    <span class="text-gray-300" aria-hidden="true">&middot;</span>
                    <span id="reading-time" aria-label="Estimated reading time">7 min read</span>
                </div>
            </header>

            <aside class="desktop-sidebar-container" id="desktop-sidebar-container">
                <div class="sidebar-content"></div>
            </aside>

            <article class="mt-8" id="article-content" role="article">
                <div class="note-status note-status--seedling" role="note" aria-label="Article status">
                    <i data-lucide="test-tube-2" aria-hidden="true"></i>
                    <div><strong>Status: Seedling.</strong> This is a developing thought. It may be incomplete or change over time.</div>
                </div>

                <p>A personal exploration into the bubbling cauldron of technologies, ideologies, and financial instruments collectively known as <span class="highlight">Web3</span>. It's more than just a buzzword; it's a paradigm shift... or is it? This is my attempt to learn in public and forge an understanding.</p>
                
                <h2 id="section-manifesto">Manifesto: Learn then Do<a href="#section-manifesto" class="heading-link" title="Copy link to section"><i data-lucide="link-2" class="w-4 h-4"></i></a></h2>
                <p>Before diving deep, it's critical to establish a guiding principle. My approach to any new, complex domain is simple: <strong>learn what's possible, then do the impossible.</strong> This means immersing oneself in the fundamentals, understanding the constraints, and only then attempting to innovate. Web3 is no exception.</p>

                <h2 id="section-core-tech">The Core Technology Stack<a href="#section-core-tech" class="heading-link" title="Copy link to section"><i data-lucide="link-2" class="w-4 h-4"></i></a></h2>
                <p>Let's dissect the beast. At its heart, Web3 is built on a few key pillars:</p>
                <ul>
                    <li><strong>Blockchain:</strong> The immutable, distributed ledger.</li>
                    <li><strong>Cryptocurrency:</strong> The native digital assets that power these networks.</li>
                    <li><strong>Smart Contracts:</strong> Self-executing contracts with code as law.</li>
                </ul>

                <blockquote cite="https://example.com">
                    <p>"The real innovation of blockchain is not just a distributed ledger, but the ability to have a shared state managed by a network of untrusted participants."</p>
                </blockquote>

                <h3 id="sub-section-contracts">A Note on Contracts<a href="#sub-section-contracts" class="heading-link" title="Copy link to section"><i data-lucide="link-2" class="w-4 h-4"></i></a></h3>
                <p>This is where the magic happens. A smart contract on a public chain is transparent and unstoppable. Its execution is guaranteed by the network, removing the need for traditional intermediaries.</p>

                <h2 id="section-implications">Implications and Skepticism<a href="#section-implications" class="heading-link" title="Copy link to section"><i data-lucide="link-2" class="w-4 h-4"></i></a></h2>
                <p>The promise is grand: a more equitable internet owned by users. But the path is fraught with challenges. Scalability, user experience, and regulatory uncertainty are significant hurdles. This journey will be documented here, in this digital garden.</p>
            </article>

            <footer class="mt-16 pt-6 border-t border-gray-200 text-sm text-gray-500">
                &copy; 2025 Forge. All rights reserved.
            </footer>
        </main>
    </div>

    <div id="mobile-nav-overlay"></div>
    <div id="mobile-nav-sheet" role="dialog" aria-modal="true" aria-labelledby="mobile-nav-title">
        <div id="mobile-nav-handle"></div>
        <h2 id="mobile-nav-title" class="sr-only">Navigation</h2>
    </div>

    <template id="sidebar-template">
        <div class="sidebar-section">
            <div class="sidebar-section-header">
                <h3 class="flex items-center gap-2"><i data-lucide="compass" class="w-4 h-4"></i> Navigation</h3>
            </div>
            <div class="sidebar-section-content">
                <a href="#" class="block text-sm text-orange-600 hover:underline mb-2">← Back to Home</a>
                <div class="relative"><i data-lucide="search" class="w-3.5 h-3.5 absolute top-1/2 left-2 -translate-y-1/2 text-gray-400"></i><input type="text" class="w-full pl-7 pr-2 py-1.5 text-xs border border-gray-300 rounded-md bg-white focus:ring-1 focus:ring-orange-400 focus:border-orange-400" placeholder="Search..."></div>
            </div>
        </div>
        <div class="sidebar-section mt-4 collapsible" data-section-id="toc-section">
            <div class="sidebar-section-header" data-toggle-section="toc-content">
                <h3 class="flex items-center gap-2"><i data-lucide="list" class="w-4 h-4"></i> Contents</h3><i data-lucide="chevron-down" class="w-4 h-4 chevron"></i>
            </div>
            <div id="toc-content" class="sidebar-section-content"><nav class="mt-2 flex flex-col space-y-1 text-sm toc-nav"></nav></div>
        </div>
        <div class="sidebar-section mt-4 collapsible collapsed" data-section-id="backlinks-section">
            <div class="sidebar-section-header" data-toggle-section="backlinks-content">
                <h3 class="flex items-center gap-2"><i data-lucide="link-2" class="w-4 h-4"></i> Referenced In</h3><i data-lucide="chevron-down" class="w-4 h-4 chevron"></i>
            </div>
            <div id="backlinks-content" class="sidebar-section-content" style="display: none;">
                <div class="mt-2 space-y-3 text-sm">
                    <div><a href="#" class="block text-orange-600 hover:underline">Q2 Reflections</a><p class="backlink-context">...deep dive into <span class="font-semibold">Web3</span> was a major focus...</p></div>
                    <div><a href="#" class="block text-orange-600 hover:underline">Digital Garden</a><p class="backlink-context">...exploring <span class="font-semibold">Web3</span> technologies and their implications...</p></div>
                </div>
            </div>
        </div>
    </template>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        lucide.createIcons();

        // --- Feature: Theme Toggle ---
        const themeToggle = document.getElementById('theme-toggle');
        const applyTheme = (theme) => {
            document.documentElement.dataset.theme = theme;
            localStorage.setItem('theme', theme);
            if (theme === 'dark') {
                document.querySelector('.dark\\:hidden').style.display = 'none';
                document.querySelector('.dark\\:block').style.display = 'block';
            } else {
                document.querySelector('.dark\\:hidden').style.display = 'block';
                document.querySelector('.dark\\:block').style.display = 'none';
            }
        };
        themeToggle.addEventListener('click', () => {
            const newTheme = document.documentElement.dataset.theme === 'dark' ? 'light' : 'dark';
            applyTheme(newTheme);
        });
        applyTheme(localStorage.getItem('theme') || 'light');

        // --- Feature: Mobile Bottom Sheet Navigation ---
        const mobileMenuBtn = document.getElementById('mobile-menu-button');
        const mobileNavSheet = document.getElementById('mobile-nav-sheet');
        const overlay = document.getElementById('mobile-nav-overlay');
        const toggleMenu = (isOpen) => {
            mobileNavSheet.classList.toggle('open', isOpen);
            overlay.classList.toggle('open', isOpen);
            mobileMenuBtn.setAttribute('aria-expanded', isOpen);
        };
        mobileMenuBtn.addEventListener('click', () => toggleMenu(true));
        overlay.addEventListener('click', () => toggleMenu(false));

        // --- Feature: Reading Time Calculation ---
        const calculateReadingTime = () => {
            const article = document.getElementById('article-content');
            if (!article) return;

            const text = article.textContent || article.innerText || '';
            const wordsPerMinute = 200;
            const words = text.trim().split(/\s+/).length;
            const readingTime = Math.ceil(words / wordsPerMinute);

            const readingTimeElement = document.getElementById('reading-time');
            if (readingTimeElement) {
                readingTimeElement.textContent = `${readingTime} min read`;
            }
        };

        // --- Feature: Reading Progress Bar ---
        const updateProgressBar = () => {
            const progressBar = document.getElementById('progress-bar');
            if (!progressBar) return;

            const scrollableHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrolled = (window.scrollY / scrollableHeight) * 100;
            progressBar.style.width = `${Math.max(0, Math.min(100, scrolled))}%`;
        };

        // --- Sidebar & TOC Initialization Logic ---
        const sidebarTemplate = document.getElementById('sidebar-template');
        if (sidebarTemplate) {
            // Populate desktop sidebar
            const desktopSidebarContent = document.querySelector('#desktop-sidebar-container .sidebar-content');
            if (desktopSidebarContent) {
                desktopSidebarContent.appendChild(sidebarTemplate.content.cloneNode(true));
                initializeSidebar(desktopSidebarContent);
            }
            // Populate mobile sheet
            mobileNavSheet.appendChild(sidebarTemplate.content.cloneNode(true));
            initializeSidebar(mobileNavSheet);
        }

        function initializeSidebar(container) {
            container.querySelectorAll('[data-toggle-section]').forEach(header => {
                header.addEventListener('click', () => {
                    const contentId = header.dataset.toggleSection;
                    const content = container.querySelector(`#${contentId}`);
                    const section = header.closest('.sidebar-section');
                    const isCollapsed = section.classList.toggle('collapsed');
                    content.style.display = isCollapsed ? 'none' : 'block';
                });
            });

            const tocNav = container.querySelector('.toc-nav');
            const articleContent = document.getElementById('article-content');
            const headings = Array.from(articleContent.querySelectorAll('h2, h3'));
            if (headings.length > 0 && tocNav) {
                const observer = new IntersectionObserver(entries => {
                    const visible = entries.find(e => e.isIntersecting);
                    document.querySelectorAll('.toc-link').forEach(link => link.classList.remove('toc-active'));
                    if (visible) {
                        document.querySelector(`.toc-link[href="#${visible.target.id}"]`)?.classList.add('toc-active');
                    }
                }, { rootMargin: "0px 0px -80% 0px", threshold: 1.0 });

                tocNav.innerHTML = ''; // Clear previous links if any
                headings.forEach(h => {
                    if (!h.id) return;
                    const link = document.createElement('a');
                    link.href = `#${h.id}`;
                    link.textContent = h.textContent.replace(/\s*\d+$/, ''); // Remove link icon text
                    link.className = `toc-link ${h.tagName.toLowerCase()}`;
                    tocNav.appendChild(link);
                    observer.observe(h);
                });
            } else {
                container.querySelector('[data-section-id="toc-section"]')?.remove();
            }
        }

        // --- Feature: Heading Links ---
        const initHeadingLinks = () => {
            document.querySelectorAll('.heading-link').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    navigator.clipboard.writeText(window.location.origin + window.location.pathname + link.getAttribute('href'));
                });
            });
        };

        // --- Feature: Scroll to Top ---
        const createScrollToTop = () => {
            const scrollBtn = document.createElement('button');
            scrollBtn.innerHTML = '<i data-lucide="arrow-up"></i>';
            scrollBtn.className = 'scroll-to-top';
            scrollBtn.setAttribute('aria-label', 'Scroll to top');
            scrollBtn.style.display = 'none';
            document.body.appendChild(scrollBtn);

            const toggleScrollBtn = () => {
                if (window.pageYOffset > 300) {
                    scrollBtn.style.display = 'flex';
                } else {
                    scrollBtn.style.display = 'none';
                }
            };

            scrollBtn.addEventListener('click', () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });

            window.addEventListener('scroll', toggleScrollBtn);
        };

        // Initialize features
        calculateReadingTime();
        updateProgressBar();
        initHeadingLinks();
        createScrollToTop();

        // Event listeners
        window.addEventListener('scroll', updateProgressBar);
    });
    </script>
</body>
</html>